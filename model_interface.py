import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
import aiohttp
from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> Dict[str, Any]:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    # Pydantic v2 has .model_json_schema()
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 极简版
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('api_timeout', 120)
            
            self.max_concurrent_requests = self.config.get('max_concurrent_requests', 20)
            self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)
            
            self.api_max_retries = self.config.get('max_retries', 3)
            self.api_retry_delay = self.config.get('retry_delay', 5)

            self.initialized = True
            logger.info(f"ModelService (KISS version) initialized: concurrent_requests={self.max_concurrent_requests}")

    async def _get_client(self) -> AsyncOpenAI:
        return AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=self.timeout,
        )

    async def generate_with_tools_async(self, messages: List[Dict], tools: List[Type[BaseModel]]):
        """
        异步调用LLM，并使用Function Calling。
        """
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas = [pydantic_to_openai_tool(tool) for tool in tools]

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        tools=tool_schemas,
                        tool_choice={"type": "function", "function": {"name": tool_schemas[0]['function']['name']}} if tool_schemas else "none",
                    )
                    await client.close()
                    return response.choices[0].message

                except Exception as e:
                    logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                    if "context canceled" in str(e).lower():
                        logger.error(f"❌ Context canceled detected on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("API call failed after multiple retries.")
                        return None
    
    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.embeddings.create(model=model_to_use, input=texts)
                    await client.close()
                    logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
                    return [item.embedding for item in response.data]

                except Exception as e:
                    logger.warning(f"⚠️ Embedding call failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Embedding call failed after multiple retries.")
                        return []
        return []

    async def rerank_async(self, query: str, documents: List[str], top_k: int = 10) -> List[Dict[str, Any]]:
        """🎯 SiliconFlow重排器API - 核心需求驱动检索组件"""
        if not documents or not self.api_key:
            logger.warning("⚠️ No documents or API key for reranking")
            return []

        # 使用SiliconFlow的重排器模型
        reranker_model = self.config.get('reranker_model', 'BAAI/bge-reranker-v2-m3')

        # 使用aiohttp进行重排器API调用
        import aiohttp

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    # 构建重排器请求URL - 基于SiliconFlow API文档
                    rerank_url = f"{self.base_url}/rerank"  # 🚀 修复：简化URL构建

                    payload = {
                        "model": reranker_model,
                        "query": query,
                        "documents": documents
                    }

                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    }

                    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                        async with session.post(rerank_url, json=payload, headers=headers) as response:
                            if response.status == 200:
                                result = await response.json()
                                results = result.get('results', [])
                                # 限制返回数量
                                limited_results = results[:top_k] if top_k else results
                                logger.info(f"🎯 Reranker processed {len(documents)} docs, returned top-{len(limited_results)}")
                                return limited_results
                            else:
                                error_text = await response.text()
                                logger.error(f"❌ Reranker API error: {response.status} - {error_text}")
                                return []

                except Exception as e:
                    logger.warning(f"⚠️ Reranker call failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Reranker call failed after multiple retries.")
                        return []
        return []

    async def generate_simple_async(self, messages: List[Dict], temperature: float = 0.1) -> str:
        """🧠 简单文本生成 - 用于元认知规划器的三阶段Prompt链"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=1000  # 适合规划阶段的输出长度
                    )
                    await client.close()

                    content = response.choices[0].message.content
                    logger.debug(f"✅ Generated {len(content)} characters of text")
                    return content or ""

                except Exception as e:
                    logger.warning(f"⚠️ Simple generation failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Simple generation failed after multiple retries.")
                        return ""
        return ""

    # ====== 🚀 真正的并发API调用 ======

    async def batch_generate_async(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """
        🚀 批量并发API调用 - 真正的并发，不等待单个响应

        Args:
            requests: 请求列表，每个请求包含 {'type': 'chat'/'embedding'/'rerank', 'params': {...}}

        Returns:
            List[Any]: 对应的响应结果列表
        """
        if not requests:
            return []

        logger.info(f"🚀 Starting batch concurrent requests: {len(requests)} requests")

        # 创建所有任务，不等待
        tasks = []
        for i, request in enumerate(requests):
            request_type = request.get('type')
            params = request.get('params', {})

            if request_type == 'chat':
                task = self._single_chat_request(params, request_id=i)
            elif request_type == 'embedding':
                task = self._single_embedding_request(params, request_id=i)
            elif request_type == 'rerank':
                task = self._single_rerank_request(params, request_id=i)
            else:
                logger.warning(f"⚠️ Unknown request type: {request_type}")
                task = asyncio.create_task(self._dummy_request())

            tasks.append(task)

        # 批量发送，等待所有结果
        logger.info(f"🚀 Sending {len(tasks)} concurrent requests...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Request {i} failed: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        logger.info(f"🎉 Batch requests completed: {success_count}/{len(requests)} successful")

        return processed_results

    async def _single_chat_request(self, params: Dict[str, Any], request_id: int = 0) -> Any:
        """单个聊天请求"""
        messages = params.get('messages', [])
        tools = params.get('tools', [])
        temperature = params.get('temperature', 0.1)

        if tools:
            return await self.generate_with_tools_async(messages, tools)
        else:
            return await self.generate_simple_async(messages, temperature)

    async def _single_embedding_request(self, params: Dict[str, Any], request_id: int = 0) -> List[List[float]]:
        """单个嵌入请求"""
        texts = params.get('texts', [])
        return await self.get_embeddings_async(texts)

    async def _single_rerank_request(self, params: Dict[str, Any], request_id: int = 0) -> List[Dict[str, Any]]:
        """单个重排请求"""
        query = params.get('query', '')
        documents = params.get('documents', [])
        top_k = params.get('top_k', 10)
        return await self.rerank_async(query, documents, top_k)

    async def _dummy_request(self) -> None:
        """虚拟请求，用于处理未知类型"""
        await asyncio.sleep(0.1)
        return None


# 全局单例
model_service = ModelService()
