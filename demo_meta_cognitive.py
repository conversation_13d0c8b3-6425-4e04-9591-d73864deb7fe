#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知分析功能演示脚本
展示super_prompt_engine中未被充分利用的高级功能
"""

import asyncio
import json
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def demo_meta_cognitive_analysis():
    """演示元认知分析的完整流程"""
    
    print("🧠 元认知分析功能演示")
    print("=" * 50)
    
    # 导入必要模块
    from super_prompt_engine import super_prompt_engine
    from config import CONFIG
    
    # 测试文本样例
    test_texts = [
        "Apple CEO <PERSON> announced new iPhone in California",
        "Microsoft Corporation acquired GitHub for $7.5 billion in June 2018",
        "Dr. <PERSON> from Stanford University published research on AI",
        "The Federal Reserve Bank raised interest rates by 0.25% yesterday"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 测试样例 {i}: {text}")
        print("-" * 40)
        
        # Step 1: 复杂度评估 (当前使用的功能)
        print("🔍 Step 1: 复杂度评估")
        assessment = super_prompt_engine.assess_complexity(text)
        print(f"  复杂度等级: {assessment.level}")
        print(f"  复杂度分数: {assessment.score:.1f}/10")
        print(f"  预期实体类型: {assessment.entity_types_expected}")
        print(f"  建议示例数量: {assessment.examples_needed}")
        print(f"  分析原因: {assessment.reasoning}")
        
        # Step 2: 元认知分析 (未使用的核心功能)
        print("\n🧠 Step 2: 元认知分析 (核心创新)")
        try:
            meta_result = await super_prompt_engine.execute_meta_cognitive_analysis(text)
            
            if meta_result.get("success"):
                print("  ✅ 元认知分析成功")
                print(f"  📋 需求描述: {meta_result.get('description', 'N/A')}")
                
                required_features = meta_result.get('required_features', {})
                entity_types = required_features.get('entity_types', [])
                structural_tags = required_features.get('structural_tags', [])
                
                print(f"  🎯 需要的实体类型: {entity_types}")
                print(f"  🏗️ 需要的结构特征: {structural_tags}")
                
                # 这里就是"主动求教"的体现：
                # LLM自主分析了文本，并生成了具体的教学需求
                print("  💡 这就是'主动求教'：LLM自主分析并生成精确的教学需求！")
                
            else:
                print(f"  ❌ 元认知分析失败: {meta_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"  ❌ 元认知分析异常: {e}")
        
        # Step 3: 检索订单生成 (未使用的功能)
        print("\n📦 Step 3: 检索订单生成")
        retrieval_order = super_prompt_engine.generate_retrieval_order(text, assessment)
        print(f"  📝 用户文本: {retrieval_order.user_text[:50]}...")
        print(f"  📊 需要示例数: {retrieval_order.num_examples_needed}")
        print("  📋 检索请求:")
        for j, request in enumerate(retrieval_order.retrieval_requests, 1):
            print(f"    {j}. 描述: {request['description']}")
            print(f"       实体类型: {request['required_features']['entity_types']}")
            print(f"       结构标签: {request['required_features']['structural_tags']}")
        
        # Step 4: 对比当前使用的直接提示
        print("\n🔄 Step 4: 当前使用的直接提示")
        direct_prompt = super_prompt_engine.generate_direct_prompt(text, assessment)
        print(f"  📝 直接提示长度: {len(direct_prompt)} 字符")
        print(f"  🎯 提示类型: 简单的实体类型列举")
        
        print("\n" + "="*50)

async def demo_retrieval_prompt():
    """演示基于检索的提示生成"""
    print("\n🔍 检索驱动提示演示")
    print("=" * 50)
    
    from super_prompt_engine import super_prompt_engine
    
    # 模拟检索到的示例
    mock_examples = [
        {
            "text": "Google CEO Sundar Pichai visited New York",
            "label": {"ORG": ["Google"], "PER": ["Sundar Pichai"], "LOC": ["New York"]}
        },
        {
            "text": "Amazon acquired Whole Foods for $13.7 billion",
            "label": {"ORG": ["Amazon", "Whole Foods"], "MONEY": ["$13.7 billion"]}
        }
    ]
    
    test_text = "Tesla CEO Elon Musk announced new factory in Texas"
    
    # 生成基于检索的提示
    retrieval_prompt = super_prompt_engine.generate_retrieval_prompt(test_text, mock_examples)
    
    print("📝 测试文本:", test_text)
    print("\n🎯 基于检索的智能提示:")
    print("-" * 30)
    print(retrieval_prompt)
    print("-" * 30)
    
    print("\n💡 这就是'需求驱动检索'的结果：")
    print("   - 不是随机示例，而是针对性的教学材料")
    print("   - 示例与目标文本在结构和实体类型上高度匹配")
    print("   - 这种方式比直接提示更精准、更有效")

async def main():
    """主演示函数"""
    print("🚀 APIICL 元认知分析功能深度演示")
    print("展示被埋没的核心创新功能")
    print("=" * 60)
    
    try:
        # 演示元认知分析
        await demo_meta_cognitive_analysis()
        
        # 演示检索驱动提示
        await demo_retrieval_prompt()
        
        print("\n🎉 演示完成！")
        print("\n💡 总结：")
        print("1. 当前只使用了30%的功能（复杂度评估 + 直接提示）")
        print("2. 元认知分析是核心创新，可以自主生成教学需求")
        print("3. 检索驱动的提示比直接提示更精准有效")
        print("4. 激活这些功能可以显著提升NER性能")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
