#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试网络连接和API可用性
"""

import asyncio
import aiohttp
import logging
from config import CONFIG

async def test_network_connection():
    """测试网络连接"""
    print("🔗 测试网络连接...")
    
    base_url = CONFIG.get('base_url')
    api_key = CONFIG.get('api_key')
    timeout = CONFIG.get('api_timeout', 300)
    
    print(f"API URL: {base_url}")
    print(f"API Key: {api_key[:10]}...")
    print(f"Timeout: {timeout}s")
    
    # 测试基本连接
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # 测试基本HTTP连接
            test_url = base_url.replace('/v1', '/health') if '/v1' in base_url else f"{base_url}/health"
            print(f"\n🔍 测试基本连接: {test_url}")
            
            try:
                async with session.get(test_url) as response:
                    print(f"✅ 基本连接成功: {response.status}")
            except Exception as e:
                print(f"❌ 基本连接失败: {e}")
            
            # 测试API端点
            print(f"\n🔍 测试API端点: {base_url}/chat/completions")
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": CONFIG.get('model_name'),
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            try:
                async with session.post(f"{base_url}/chat/completions", 
                                      json=payload, 
                                      headers=headers,
                                      timeout=aiohttp.ClientTimeout(total=30)) as response:
                    print(f"✅ API端点响应: {response.status}")
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ API调用成功: {result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')}")
                    else:
                        text = await response.text()
                        print(f"❌ API错误响应: {text[:200]}")
            except Exception as e:
                print(f"❌ API端点失败: {e}")
                
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")

async def test_model_interface():
    """测试model_interface"""
    print("\n🧠 测试model_interface...")
    
    try:
        from model_interface import model_service
        
        # 测试简单生成
        messages = [{"role": "user", "content": "Say 'Hello World'"}]
        response = await model_service.generate_simple_async(messages)
        
        if response:
            print(f"✅ model_interface成功: {response[:50]}...")
        else:
            print("❌ model_interface失败: 空响应")
            
    except Exception as e:
        print(f"❌ model_interface异常: {e}")

async def main():
    """主测试函数"""
    print("🔍 API连接诊断工具")
    print("=" * 50)
    
    await test_network_connection()
    await test_model_interface()
    
    print("\n" + "=" * 50)
    print("🔧 可能的解决方案:")
    print("1. 检查网络连接")
    print("2. 验证API服务器状态")
    print("3. 确认API密钥有效性")
    print("4. 检查防火墙设置")
    print("5. 尝试更换网络环境")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
