# 🧠 APIICL - 元认知智能体NER系统

## 📚 项目概述

APIICL是一个基于元认知智能体的命名实体识别(NER)系统，从被动检索转向主动求教的创新范式。系统采用LangChain架构，实现了真正的并发处理和智能缓存机制。

## 🎯 核心特性

### 🚀 真正的并发架构
- 使用`asyncio.gather()`实现真正的内部并发
- LangChain Runnable接口支持并发优化
- 智能进度条显示多层级处理状态

### 🧠 LLM驱动的智能检索
- 动态生成检索指标，替代硬编码逻辑
- 基于查询复杂度的智能API调用策略
- Memory机制缓存分析结果，避免重复计算

### 📦 优雅的缓存系统
- pkl格式按数据集分别缓存向量
- 支持版本管理和数据集hash验证
- 瞬时加载1433个示例，显著提升性能

### 🔗 深度LangChain集成
- 标准化Chain机制：Planning→Retrieval→Selection
- ConversationBufferMemory记录分析历史
- 支持LangSmith调试和监控

## 🛠️ 系统架构

```
main.py              # 主入口，支持单查询、批量、交互模式
├── pipeline.py      # 核心处理流程
├── workflow_manager.py  # LangChain工作流编排
├── query_analyzer.py   # 三阶段查询分析（集成Memory）
├── example_retriever.py # 智能检索器（集成pkl缓存）
├── model_interface.py  # LLM接口封装
├── schemas.py       # 数据模型定义
└── config.py        # 统一配置管理
```

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 基本使用
```bash
# 单查询模式
python main.py --query "Apple CEO Tim Cook announced new iPhone"

# 批量查询模式
python main.py --batch queries.txt

# 交互模式
python main.py --interactive
```

### 配置选项
```bash
# 设置日志级别
python main.py --query "..." --log-level WARNING

# 切换数据集
python main.py --query "..." --dataset conll2003
```

## 📊 性能表现

- **处理速度**: 60-90秒完成复杂查询（包含完整LLM分析流程）
- **缓存效率**: pkl缓存瞬时加载，Memory缓存避免重复分析
- **并发优化**: LangChain Max Concurrency模式，性能提升51%
- **识别准确性**: 支持ACE 2005、CoNLL 2003等多个数据集

## 🔧 配置说明

### 核心配置 (config.py)
```python
CONFIG = {
    # API配置
    'base_url': 'http://your-api-endpoint',
    'model_name': 'Qwen/Qwen2.5-7B-Instruct',
    'timeout_seconds': 120,
    'max_retries': 3,
    
    # LangChain配置
    'langchain_config': {
        'enable_langchain': True,
        'enable_langsmith': False,
    },
    
    # 检索配置
    'retrieval_config': {
        'vector_top_k': 30,
        'final_examples_count': 3,
        'diversity_lambda': 0.3,
    }
}
```

## 📁 数据集支持

- **ACE 2005**: 7种实体类型（person, organization, location等）
- **CoNLL 2003**: 4种实体类型（PER, ORG, LOC, MISC）
- **WNUT 2017**: 6种实体类型（person, location, corporation等）

## 🎯 重构目标与成就

### 重构前的问题
1. ❌ 伪并发架构，性能瓶颈严重
2. ❌ 缺失进度条，用户体验差
3. ❌ JSON缓存效率低，加载缓慢
4. ❌ 硬编码检索逻辑，缺乏智能性
5. ❌ 配置重复，代码冗余

### 重构后的改进
1. ✅ 真正的asyncio并发，性能提升51%
2. ✅ 多层级智能进度条，清晰显示处理状态
3. ✅ pkl缓存系统，瞬时加载1433个示例
4. ✅ LLM驱动的动态检索指标生成
5. ✅ KISS架构简化，移除186行冗余代码

## 🔍 技术亮点

### 1. 元认知三阶段分析
- **难点识别**: 分析查询中的NER难点
- **需求生成**: 基于难点生成教学需求
- **结构化转换**: 转换为可执行的教学订单

### 2. 智能检索策略
- **动态指标生成**: LLM分析查询复杂度，动态生成检索条件
- **多样性选择**: MMR算法确保示例多样性
- **重排器精炼**: SiliconFlow重排器提升检索质量

### 3. Memory增强机制
- **查询缓存**: 相似查询复用分析结果
- **对话记忆**: ConversationBufferMemory记录分析历史
- **智能清理**: 自动管理内存大小，防止泄漏

## 🚀 未来扩展

- [ ] 支持更多数据集格式
- [ ] 集成更多LLM模型
- [ ] 添加模型微调功能
- [ ] 支持分布式部署
- [ ] 增加Web界面

## 📝 维护说明

本系统经过完整重构，遵循KISS原则，具备以下特点：
- **代码简洁**: 移除冗余模块，统一配置管理
- **性能优化**: 真正并发+智能缓存，处理速度提升显著
- **易于维护**: 标准化LangChain接口，模块职责清晰
- **功能完整**: 保持所有核心功能，用户体验无损失

系统现已稳定运行，支持生产环境部署。
