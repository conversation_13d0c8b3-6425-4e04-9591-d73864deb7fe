#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API连接
"""

import asyncio
import logging
from model_interface import model_service

async def test_api():
    """测试API连接"""
    try:
        print("🔗 测试API连接...")
        
        messages = [{"role": "user", "content": "Hello, please respond with 'API connection successful'"}]
        
        response = await model_service.generate_simple_async(messages)
        
        print(f"✅ API响应: {response}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

async def main():
    success = await test_api()
    if success:
        print("🎉 API连接正常")
    else:
        print("💥 API连接有问题")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
