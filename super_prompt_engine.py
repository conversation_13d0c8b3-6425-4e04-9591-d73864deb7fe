"""
超级提示引擎 - 简单高效的元认知分析
遵循KISS原则：保持简单，避免过度设计
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from config import CONFIG
from schemas import ExtractionTool

logger = logging.getLogger(__name__)

@dataclass
class ComplexityAssessment:
    """文本复杂度评估结果"""
    level: str  # 'simple', 'medium', 'complex'
    score: float  # 1-10分数
    entity_types_expected: List[str]
    examples_needed: int
    reasoning: str

@dataclass
class RetrievalOrder:
    """多维检索订单"""
    user_text: str
    num_examples_needed: int
    retrieval_requests: List[Dict[str, Any]]

class SuperPromptEngine:
    """简单的超级提示引擎"""
    
    def __init__(self):
        self.config = CONFIG.get('super_prompt_config', {})
        self.mode = self.config.get('mode', 'direct')  # 'direct' or 'retrieval'
        
    def assess_complexity(self, text: str) -> ComplexityAssessment:
        """简单的复杂度评估"""
        text_len = len(text)
        word_count = len(text.split())
        sentence_count = len([s for s in text.split('.') if s.strip()])

        # 基本文本特征
        length_score = min(text_len / 30, 4)
        word_density_score = min(word_count / 15, 2)

        # 实体类型预测
        expected_types = []
        entity_complexity_score = 0

        # 检测专有名词
        words = text.split()
        proper_nouns = [w for w in words if w and w[0].isupper() and len(w) > 1]
        if len(proper_nouns) >= 2:
            entity_complexity_score += 1
            if 'person' not in expected_types:
                expected_types.append('person')

        # 检测组织机构模式
        org_patterns = ['Corporation', 'Inc.', 'Ltd.', 'Company', 'University', 'Bank']
        if any(pattern in text for pattern in org_patterns):
            entity_complexity_score += 1
            if 'organization' not in expected_types:
                expected_types.append('organization')

        # 传统关键词检测
        entity_indicators = {
            'person': ['Mr.', 'Mrs.', 'Dr.', 'Prof.', 'President', 'CEO'],
            'organization': ['Inc.', 'Corp.', 'Ltd.', 'Company', 'University', 'Bank'],
            'location': ['City', 'State', 'County', 'Street', 'Avenue'],
            'misc': ['$', '%', 'million', 'billion', 'percent']
        }

        for entity_type, indicators in entity_indicators.items():
            if any(indicator in text for indicator in indicators):
                if entity_type not in expected_types:
                    expected_types.append(entity_type)
                entity_complexity_score += 0.5

        # 结构复杂度
        structure_score = 0
        if ',' in text:
            structure_score += 0.5  # 复合句
        if ';' in text:
            structure_score += 0.5  # 复杂句
        if len(text.split('.')) > 2:
            structure_score += 0.5  # 多句子

        # 计算最终复杂度
        total_score = length_score + word_density_score + entity_complexity_score + structure_score
        
        if total_score < 3:
            complexity_level = 'simple'
            examples_needed = 2
        elif total_score < 6:
            complexity_level = 'medium'
            examples_needed = 3
        else:
            complexity_level = 'complex'
            examples_needed = 4

        reasoning = f"长度: {length_score:.1f}, 实体: {entity_complexity_score:.1f}, 结构: {structure_score:.1f}"

        return ComplexityAssessment(
            level=complexity_level,
            score=total_score,
            entity_types_expected=expected_types,
            examples_needed=examples_needed,
            reasoning=reasoning
        )

    def generate_meta_cognitive_prompt(self, text: str) -> str:
        """生成元认知超级提示（保持英文prompt）"""
        
        prompt = f"""
You are an expert NER task planner. Analyze the input text, identify NER challenges, and generate a structured retrieval order.

Input text: {text}

Please complete the following tasks in one go:
1. Analyze NER challenges in the text (e.g., nested entities, entity abbreviations, boundary ambiguity)
2. Generate natural language description of what teaching examples are needed
3. Determine required fact tags (entity_types, structural_tags)
4. Call retrieve_ner_examples function to generate retrieval order

Available fact tags:
- entity_types: ["PER", "ORG", "LOC", "MISC"] (entity types)
- structural_tags: ["passive_voice", "has_numbers", "has_abbreviations"] (structural tags)

Example analysis:
User input: "Apple CEO Tim Cook announced new iPhone in California"
Analysis result:
- NER challenges: Organization abbreviation (Apple), Person name (Tim Cook), Location (California)
- Description: "Business news containing organization abbreviations and person names"
- Fact tags: {{"entity_types": ["ORG", "PER", "LOC"], "structural_tags": ["has_abbreviations"]}}

Please generate similar analysis and retrieval order for the above input text.
"""
        return prompt
    
    def generate_retrieval_function_schema(self) -> Dict:
        """生成检索函数JSON模式"""
        return {
            "name": "retrieve_ner_examples",
            "description": "Retrieve NER examples based on description and fact tags",
            "parameters": {
                "type": "object",
                "properties": {
                    "description": {
                        "type": "string",
                        "description": "Natural language description of needed teaching examples"
                    },
                    "required_features": {
                        "type": "object",
                        "description": "Required fact tags",
                        "properties": {
                            "entity_types": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Required entity types"
                            },
                            "structural_tags": {
                                "type": "array", 
                                "items": {"type": "string"},
                                "description": "Required structural tags"
                            }
                        }
                    }
                },
                "required": ["description", "required_features"]
            }
        }

    async def execute_meta_cognitive_analysis(self, text: str) -> Dict[str, Any]:
        """执行完整的元认知分析 - 单一调用"""
        try:
            from model_interface import model_service

            # 生成超级提示
            prompt = self.generate_meta_cognitive_prompt(text)

            # 准备函数调用
            function_schema = self.generate_retrieval_function_schema()

            # 调用LLM进行单一分析
            messages = [{"role": "user", "content": prompt}]

            response = await model_service.generate_with_tools_async(
                messages=messages,
                tools=[function_schema],
                tool_choice={"type": "function", "function": {"name": "retrieve_ner_examples"}}
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    import json
                    args = json.loads(tool_call.function.arguments)
                    return {
                        "success": True,
                        "description": args.get("description", ""),
                        "required_features": args.get("required_features", {}),
                        "raw_response": response
                    }

            return {
                "success": False,
                "error": "Failed to generate retrieval order",
                "raw_response": response
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def generate_direct_prompt(self, text: str, assessment: ComplexityAssessment) -> str:
        """生成直接NER提示（保持英文prompt）"""
        prompt = f"""
You are an expert Named Entity Recognition system.

Extract named entities from the following text using the specified entity types.

Text to analyze: {text}

Expected entity types: {', '.join(assessment.entity_types_expected)}
Complexity level: {assessment.level}

Please extract all named entities and return them in JSON format.
"""
        return prompt

    def generate_retrieval_order(self, text: str, assessment: ComplexityAssessment) -> RetrievalOrder:
        """根据评估生成检索订单"""
        retrieval_requests = []
        
        # 根据复杂度生成请求
        if assessment.level == 'simple':
            retrieval_requests.append({
                "description": f"Simple examples with {', '.join(assessment.entity_types_expected)} entities",
                "required_features": {
                    "entity_types": assessment.entity_types_expected,
                    "structural_tags": []
                }
            })
        elif assessment.level == 'medium':
            retrieval_requests.append({
                "description": f"Medium complexity examples with {', '.join(assessment.entity_types_expected)} entities",
                "required_features": {
                    "entity_types": assessment.entity_types_expected,
                    "structural_tags": ["has_numbers"]
                }
            })
        else:  # complex
            retrieval_requests.append({
                "description": f"Complex examples with {', '.join(assessment.entity_types_expected)} entities",
                "required_features": {
                    "entity_types": assessment.entity_types_expected,
                    "structural_tags": ["has_abbreviations", "has_numbers"]
                }
            })
        
        return RetrievalOrder(
            user_text=text,
            num_examples_needed=assessment.examples_needed,
            retrieval_requests=retrieval_requests
        )

    def generate_retrieval_prompt(self, text: str, examples: List[Dict[str, Any]]) -> str:
        """生成基于检索的NER提示（保持英文prompt）"""
        examples_str = ""
        for i, example in enumerate(examples):
            text_content = example.get('text', '')
            labels = example.get('label', {})
            entities_str = ", ".join(
                f"'{value}' ({etype})" 
                for etype, values in labels.items() 
                for value in values
            )
            examples_str += f"Example {i+1}:\nInput: {text_content}\nOutput: [{entities_str}]\n\n"
        
        prompt = f"""
You are an expert Named Entity Recognition system.

Learn from these examples:

{examples_str}

Now extract named entities from this text:

{text}

Return the entities in JSON format.
"""
        return prompt


# 全局实例
super_prompt_engine = SuperPromptEngine()
