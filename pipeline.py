import json
import logging
import re
from typing import Dict, List
from tqdm.asyncio import tqdm

from config import CONFIG
from schemas import ExtractionTool
from model_interface import model_service
# 注释：workflow_manager和example_retriever已被超级Prompt替代

# --- 核心模块 ---

def clean_json_string(json_str: str) -> str:
    """清理JSON字符串，移除尾随逗号等常见错误"""
    if not json_str:
        return json_str
    
    # 移除尾随逗号
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
    
    # 移除对象末尾的逗号
    json_str = re.sub(r',(\s*})', r'\1', json_str)
    
    # 移除数组末尾的逗号
    json_str = re.sub(r',(\s*\])', r'\1', json_str)
    
    return json_str.strip()


def get_intent(query: str) -> str:
    """
    通过关键词快速识别用户查询的意图。
    """
    strategies = CONFIG.get('intent_strategies', {})
    for intent, config in strategies.items():
        if 'keywords' in config:
            for keyword in config['keywords']:
                if keyword in query:
                    return intent
    return 'default'

def load_samples_from_file(filepath: str) -> List[Dict]:
    """从JSON文件加载样本。"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logging.warning(f"Sample file not found: {filepath}, skipping.")
        return []
    except json.JSONDecodeError:
        logging.error(f"Could not decode JSON from {filepath}.")
        return []

class SampleSelector:
    """
    根据意图和策略，选择并组合ICL样本。
    """
    def __init__(self):
        self.axiomatic_pools = {}
        strategies = CONFIG.get('intent_strategies', {})
        for intent, config in strategies.items():
            pool_config = config.get('sample_pools', {}).get('axiomatic')
            if pool_config and 'file' in pool_config:
                self.axiomatic_pools[intent] = load_samples_from_file(pool_config['file'])
        
        self.semantic_pool = load_samples_from_file("data/CoNLL2003/train.json")

    def select_samples(self, query: str, intent: str) -> List[Dict]:
        """选择样本的核心逻辑。"""
        strategy = CONFIG['intent_strategies'].get(intent, CONFIG['intent_strategies']['default'])
        pools_config = strategy['sample_pools']
        
        selected_samples = []

        if 'axiomatic' in pools_config and intent in self.axiomatic_pools:
            pool = self.axiomatic_pools[intent]
            count = pools_config['axiomatic'].get('count', 0)
            if pool and count > 0:
                selected_samples.extend(pool[:count])

        if 'semantic' in pools_config:
            count = pools_config['semantic'].get('count', 0)
            if self.semantic_pool and count > 0:
                selected_samples.extend(self.semantic_pool[-count:])
        
        return selected_samples

def format_examples_for_prompt(samples: List[Dict]) -> str:
    """将样本格式化为清晰的字符串，用于Prompt。"""
    if not samples:
        return "No examples provided."
    
    formatted = []
    for sample in samples:
        text = sample.get('text', '')
        labels = sample.get('label', {})
        if not isinstance(labels, dict):
             continue

        entities_str = ", ".join(
            f"'{value}' ({etype})" 
            for etype, values in labels.items() 
            for value in values
        )
        formatted.append(f"Input: {text}\nOutput: [{entities_str}]")
    
    return "\n---\n".join(formatted)

# --- 🚀 智能体NER流程 (您的创新方案) ---

async def run_meta_cognitive_ner_pipeline(query: str) -> Dict[str, List[str]]:
    """
    🚀 超级Prompt NER流水线 - 一次性完成NER任务

    核心优化：
    1. 跳过复杂的多步骤检索流程
    2. 使用超级Prompt直接进行NER
    3. 大幅提升性能和准确率
    """
    try:
        # 🎯 创建简化进度条
        show_progress = CONFIG.get('show_progress', True)
        progress_desc = CONFIG.get('progress_desc', '🚀 Super Prompt NER')

        if show_progress:
            main_pbar = tqdm(total=2, desc=progress_desc, leave=False)
        else:
            main_pbar = None

        logging.info(f"🚀 Starting super prompt NER for: '{query[:50]}...'")

        # Step 1: 使用超级Prompt引擎
        if main_pbar:
            main_pbar.set_description("🧠 Generating super prompt")
            main_pbar.update(1)

        from super_prompt_engine import super_prompt_engine

        # 评估复杂度并生成超级Prompt
        assessment = super_prompt_engine.assess_complexity(query)
        prompt = super_prompt_engine.generate_direct_prompt(query, assessment)

        logging.info(f"🧠 Complexity assessment: {assessment.level} ({assessment.score:.1f}/10)")

        # Step 2: 直接调用LLM进行NER
        if main_pbar:
            main_pbar.set_description("🤖 Direct NER inference")

        messages = [{"role": "user", "content": prompt}]

        # 调用LLM进行NER推理
        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=[ExtractionTool]
        )

        if main_pbar:
            main_pbar.update(1)
            main_pbar.set_description("✅ NER completed")
            main_pbar.close()

        if response and response.tool_calls:
            tool_call = response.tool_calls[0]
            if tool_call.function and tool_call.function.arguments:
                try:
                    # 清理JSON字符串
                    cleaned_args = clean_json_string(tool_call.function.arguments)
                    tool_instance = ExtractionTool.model_validate_json(cleaned_args)
                    entities = tool_instance.entities

                    # 记录成功的超级Prompt决策
                    total_entities = sum(len(v) for v in entities.values())
                    logging.info(f"✅ Super Prompt NER completed: {total_entities} entities found, complexity: {assessment.level}")

                    return entities
                except Exception as pydantic_error:
                    logging.error(f"❌ Pydantic validation failed: {pydantic_error}")
                    if main_pbar and not main_pbar.disable:
                        main_pbar.close()
                    return {}

        logging.warning("⚠️ No valid tool calls in response, using fallback")
        if main_pbar and not main_pbar.disable:
            main_pbar.close()
        return await _fallback_ner_pipeline(query)

    except Exception as e:
        # 确保进度条正确关闭
        try:
            if 'main_pbar' in locals() and main_pbar and not main_pbar.disable:
                main_pbar.close()
        except:
            pass
        logging.error(f"❌ Super Prompt NER pipeline failed: {e}", exc_info=True)
        return await _fallback_ner_pipeline(query)


async def _fallback_ner_pipeline(query: str) -> Dict[str, List[str]]:
    """🔄 回退到传统NER流程"""
    try:
        logging.info("🔄 Using fallback NER pipeline")

        # 使用简单的默认示例
        fallback_examples = [
            {
                "text": "Apple Inc. is a technology company.",
                "label": {"ORG": ["Apple Inc."]}
            }
        ]

        examples_str = format_examples_for_prompt(fallback_examples)
        prompt = _get_optimized_ner_prompt(examples_str, query)
        messages = [{"role": "user", "content": prompt}]

        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=[ExtractionTool]
        )

        if response and response.tool_calls:
            tool_call = response.tool_calls[0]
            if tool_call.function and tool_call.function.arguments:
                try:
                    # 清理JSON字符串
                    cleaned_args = clean_json_string(tool_call.function.arguments)
                    tool_instance = ExtractionTool.model_validate_json(cleaned_args)
                    return tool_instance.entities
                except Exception:
                    return {}
        return {}

    except Exception as e:
        logging.error(f"❌ Even fallback pipeline failed: {e}")
        return {}


def _get_optimized_ner_prompt(examples_str: str, query: str) -> str:
    """🎯 获取优化的NER提示模板"""
    from config import get_optimized_label_prompt
    label_set_info = get_optimized_label_prompt()

    return f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below:
{label_set_info}

### Context and Examples ###
{examples_str}

### Text to Analyze ###
{query}

OUTPUT FORMAT: Return a JSON object where keys are entity types and values are arrays of entity strings.
- If entities are found: {{"PER": ["John", "Mary"], "ORG": ["Apple"]}}
- If no entities are found: {{}}
- Only include entity types that have found entities
- Do not include empty arrays

OUTPUT: Valid JSON only. No explanations."""


# --- 🔄 传统流程保留 (向后兼容) ---

async def run_ner_pipeline(query: str, sample_selector: SampleSelector) -> Dict[str, List[str]]:
    """
    🔄 传统NER流水线 (保留用于向后兼容)
    """
    intent = get_intent(query)
    logging.info(f"🔄 Traditional pipeline - Query: '{query[:30]}...' -> Intent: {intent}")

    icl_samples = sample_selector.select_samples(query, intent)
    examples_str = format_examples_for_prompt(icl_samples)

    prompt = CONFIG['system_prompt_template'].format(
        examples_str=examples_str,
        user_query=query
    )

    messages = [{"role": "user", "content": prompt}]

    try:
        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=[ExtractionTool]
        )

        if response and response.tool_calls:
            tool_call = response.tool_calls[0]
            if tool_call.function and tool_call.function.arguments:
                try:
                    # 清理JSON字符串
                    cleaned_args = clean_json_string(tool_call.function.arguments)
                    tool_instance = ExtractionTool.model_validate_json(cleaned_args)
                    return tool_instance.entities
                except Exception as pydantic_error:
                    logging.error(f"Pydantic validation failed: {pydantic_error}")
                    return {}
        return {}
    except Exception as e:
        logging.error(f"Error during LLM call for query '{query}': {e}", exc_info=True)
        return {}

# 🎯 pipeline.py 现在是纯功能模块
# 使用方式：
# from pipeline import run_meta_cognitive_ner_pipeline
# result = await run_meta_cognitive_ner_pipeline("your query here")
